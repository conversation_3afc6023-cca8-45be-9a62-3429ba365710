<template>
  <el-checkbox-group v-model="internalValue" @change="handleChange">
    <el-checkbox
      v-for="option in options"
      :key="option.value"
      :label="option.value"
      :disabled="disabled"
    >
      {{ option.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script setup>
import { ref, watch } from "vue";

const props = defineProps({
  options: {
    type: Array,
    default: () => [
      { label: "选项1", value: "option1" },
      { label: "选项2", value: "option2" },
      { label: "选项3", value: "option3" },
    ],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  defaultValue: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["change", "update:modelValue"]);

const internalValue = ref([...props.defaultValue]);

// 监听默认值变化
watch(
  () => props.defaultValue,
  (newValue) => {
    internalValue.value = [...newValue];
  },
  { deep: true }
);

// 监听外部modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== undefined) {
      internalValue.value = [...newValue];
    }
  },
  { deep: true, immediate: true }
);

// 监听内部值变化，向外发射
watch(
  internalValue,
  (newValue) => {
    emit("update:modelValue", newValue);
  },
  { deep: true }
);

const handleChange = (value) => {
  internalValue.value = value;
  emit("change", value);
  emit("update:modelValue", value);
};
</script>

<style scoped>
.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-checkbox {
  margin-right: 0;
}
</style>
